// app/api/orders/action/route.ts
import { NextRequest, NextResponse } from 'next/server';

const WEBHOOK_ORDERS_ACTION = process.env.NEXT_PUBLIC_WEBHOOK_URL_ORDERS_ACTION;
const WEBHOOK_ACCESS_TOKEN = process.env.WEBHOOK_ACCESS_TOKEN;

export async function POST(request: NextRequest) {
  try {
    if (!WEBHOOK_ORDERS_ACTION) {
      return NextResponse.json(
        { error: true, message: 'Orders action URL not configured' },
        { status: 500 }
      );
    }

    const body = await request.json();
    
    // Prepare headers
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Add access token if available
    if (WEBHOOK_ACCESS_TOKEN) {
      headers['Authorization'] = `Bearer ${WEBHOOK_ACCESS_TOKEN}`;
    }

    const response = await fetch(WEBHOOK_ORDERS_ACTION, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    return NextResponse.json(data, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error('Failed to execute orders action:', error);
    return NextResponse.json(
      { 
        error: true, 
        message: error instanceof Error ? error.message : 'Failed to execute orders action' 
      },
      { status: 500 }
    );
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
