// components/TabDiscordBot.tsx
"use client";
import { useState, useEffect } from "react";
import { DiscordBotConfig, DISCORD_PERMISSIONS, DEFAULT_PATTERNS } from "@/types/discord";
import { LOT_DISTRIBUTION_CONFIG } from "@/config/global";
import Select from "react-select";
import AlertDialog from "./AlertDialog";
import ConfirmationDialog from "./ConfirmationDialog";
import { useAlert } from "@/hooks/useAlert";

const WEBHOOK_GROUP_2 = process.env.NEXT_PUBLIC_WEBHOOK_URL_GROUP2;

export default function TabDiscordBot({
  customStyle,
  sendWebhook,
  loading,
}: {
  customStyle: Object;
  sendWebhook: Function;
  loading: boolean;
}) {
  const [bots, setBots] = useState<DiscordBotConfig[]>([]);
  const [showAddBot, setShowAddBot] = useState(false);
  const [editingBot, setEditingBot] = useState<string | null>(null);
  const [newBot, setNewBot] = useState<Partial<DiscordBotConfig>>({
    name: "",
    serverId: "",
    channelId: "",
    clientId: "",
    botToken: "",
    webhookUrl: WEBHOOK_GROUP_2 || "",
    permissions: ["VIEW_CHANNEL", "READ_MESSAGE_HISTORY"],
    enabled: false,
    patterns: DEFAULT_PATTERNS,
    lotSettings: LOT_DISTRIBUTION_CONFIG,
  });
  const [botTokens, setBotTokens] = useState<Record<string, string>>({});
  const [inviteLinks, setInviteLinks] = useState<Record<string, string>>({});

  // Alert hook for beautiful dialogs
  const { alert, showSuccess, showError, showWarning, closeAlert } = useAlert();

  // Confirmation dialog state
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [botToDelete, setBotToDelete] = useState<string | null>(null);

  useEffect(() => {
    loadBots();
  }, []);

  const loadBots = async () => {
    try {
      const response = await fetch('/api/discord/bots');
      if (response.ok) {
        const data = await response.json();
        setBots(data);
      }
    } catch (error) {
      console.error('Failed to load bots:', error);
    }
  };

  const saveBots = async (updatedBots: DiscordBotConfig[]) => {
    try {
      await fetch('/api/discord/bots', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedBots),
      });
    } catch (error) {
      console.error('Failed to save bots:', error);
    }
  };

  const addBot = () => {
    if (!newBot.name || !newBot.serverId || !newBot.channelId) {
      showError('Please fill in all required fields');
      return;
    }

    const botConfig: DiscordBotConfig = {
      id: Date.now().toString(),
      name: newBot.name,
      enabled: false,
      serverId: newBot.serverId,
      channelId: newBot.channelId,
      clientId: newBot.clientId,
      botToken: newBot.botToken,
      webhookUrl: newBot.webhookUrl || WEBHOOK_GROUP_2 || "",
      permissions: newBot.permissions || ["VIEW_CHANNEL", "READ_MESSAGE_HISTORY"],
      createdAt: new Date().toISOString(),
    };

    const updatedBots = [...bots, botConfig];
    setBots(updatedBots);
    saveBots(updatedBots);
    
    setNewBot({
      name: "",
      serverId: "",
      channelId: "",
      clientId: "",
      botToken: "",
      webhookUrl: WEBHOOK_GROUP_2 || "",
      permissions: ["VIEW_CHANNEL", "READ_MESSAGE_HISTORY"],
      enabled: false,
      patterns: DEFAULT_PATTERNS,
      lotSettings: LOT_DISTRIBUTION_CONFIG,
    });
    setShowAddBot(false);
  };

  const toggleBot = async (botId: string) => {
    const bot = bots.find(b => b.id === botId);
    if (!bot) return;

    try {
      if (bot.enabled) {
        // Stop bot
        const response = await fetch('/api/discord/control', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ action: 'stop', botId }),
        });

        if (response.ok) {
          const updatedBots = bots.map(b =>
            b.id === botId ? { ...b, enabled: false } : b
          );
          setBots(updatedBots);
          saveBots(updatedBots);
        } else {
          const error = await response.json();
          showError(`Failed to stop bot: ${error.error}`);
        }
      } else {
        // Start bot - use stored token or fallback to manual input
        let token = bot.botToken || botTokens[botId];
        if (!token) {
          showError('Please configure bot token in the bot settings first');
          return;
        }

        const response = await fetch('/api/discord/control', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ action: 'start', botId, token }),
        });

        if (response.ok) {
          const updatedBots = bots.map(b =>
            b.id === botId ? { ...b, enabled: true, lastActive: new Date().toISOString() } : b
          );
          setBots(updatedBots);
          saveBots(updatedBots);
          showSuccess('Bot started successfully!');
        } else {
          const error = await response.json();
          showError(`Failed to start bot: ${error.error}`);
        }
      }
    } catch (error) {
      console.error('Error toggling bot:', error);
      showError('An error occurred while toggling the bot');
    }
  };

  const deleteBot = (botId: string) => {
    setBotToDelete(botId);
    setShowDeleteConfirm(true);
  };

  const confirmDeleteBot = async () => {
    if (!botToDelete) return;

    // Stop bot first if it's running
    const bot = bots.find(b => b.id === botToDelete);
    if (bot?.enabled) {
      await fetch('/api/discord/control', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'stop', botId: botToDelete }),
      });
    }

    const updatedBots = bots.filter(b => b.id !== botToDelete);
    setBots(updatedBots);
    saveBots(updatedBots);

    setShowDeleteConfirm(false);
    setBotToDelete(null);
    showSuccess('Bot deleted successfully');
  };

  const cancelDeleteBot = () => {
    setShowDeleteConfirm(false);
    setBotToDelete(null);
  };

  const generateInviteLink = (bot: DiscordBotConfig) => {
    let clientId = bot.clientId;
    if (!clientId) {
      const promptResult = prompt('Enter your Discord Application Client ID:');
      if (promptResult) {
        clientId = promptResult;
      }
    }
    if (clientId) {
      const link = generateDiscordInviteLink(clientId, bot.permissions);
      setInviteLinks(prev => ({ ...prev, [bot.id]: link }));
    }
  };

  const generateDiscordInviteLink = (clientId: string, permissions: string[]): string => {
    const permissionMap: Record<string, string> = {
      'VIEW_CHANNEL': '1024',
      'READ_MESSAGE_HISTORY': '65536',
      'SEND_MESSAGES': '2048',
      'MANAGE_MESSAGES': '8192',
      'EMBED_LINKS': '16384',
      'ATTACH_FILES': '32768',
      'USE_EXTERNAL_EMOJIS': '262144',
      'ADD_REACTIONS': '64',
      'MENTION_EVERYONE': '131072',
      'MANAGE_WEBHOOKS': '536870912',
      'USE_SLASH_COMMANDS': '2147483648'
    };

    let permissionValue = 0;
    permissions.forEach(permission => {
      if (permissionMap[permission]) {
        permissionValue += parseInt(permissionMap[permission]);
      }
    });

    return `https://discord.com/api/oauth2/authorize?client_id=${clientId}&permissions=${permissionValue}&scope=bot`;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    showSuccess('Copied to clipboard!');
  };

  const startEditBot = (bot: DiscordBotConfig) => {
    setEditingBot(bot.id);
    setNewBot({
      ...bot,
      patterns: bot.patterns || DEFAULT_PATTERNS,
      lotSettings: bot.lotSettings || LOT_DISTRIBUTION_CONFIG,
    });
  };

  const cancelEdit = () => {
    setEditingBot(null);
    setNewBot({
      name: "",
      serverId: "",
      channelId: "",
      clientId: "",
      botToken: "",
      webhookUrl: WEBHOOK_GROUP_2 || "",
      permissions: ["VIEW_CHANNEL", "READ_MESSAGE_HISTORY"],
      enabled: false,
      patterns: DEFAULT_PATTERNS,
      lotSettings: LOT_DISTRIBUTION_CONFIG,
    });
  };

  const saveEditBot = () => {
    if (!editingBot || !newBot.name || !newBot.serverId || !newBot.channelId) {
      showError('Please fill in all required fields');
      return;
    }

    const updatedBots = bots.map(bot =>
      bot.id === editingBot
        ? {
            ...bot,
            ...newBot,
            patterns: newBot.patterns || DEFAULT_PATTERNS,
            lotSettings: newBot.lotSettings || LOT_DISTRIBUTION_CONFIG,
          } as DiscordBotConfig
        : bot
    );

    setBots(updatedBots);
    saveBots(updatedBots);
    setEditingBot(null);
    cancelEdit();
  };

  const setDefaultPatterns = () => {
    setNewBot(prev => ({
      ...prev,
      patterns: DEFAULT_PATTERNS
    }));
  };

  const setDefaultLotSettings = () => {
    setNewBot(prev => ({
      ...prev,
      lotSettings: LOT_DISTRIBUTION_CONFIG
    }));
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold text-white">Discord Bot Manager</h2>
        <button
          onClick={() => setShowAddBot(!showAddBot)}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          {showAddBot ? 'Cancel' : '+ Add Bot'}
        </button>
      </div>

      {/* Add/Edit Bot Form */}
      {(showAddBot || editingBot) && (
        <div className="bg-gray-800 p-4 rounded-lg border border-gray-600">
          <h3 className="text-lg font-semibold text-white mb-4">
            {editingBot ? 'Edit Discord Bot' : 'Add New Discord Bot'}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-200 mb-1">
                Bot Name *
              </label>
              <input
                type="text"
                value={newBot.name || ""}
                onChange={(e) => setNewBot(prev => ({ ...prev, name: e.target.value }))}
                className="w-full rounded-md bg-gray-700 text-white border border-gray-600 p-2"
                placeholder="My Trading Bot"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-200 mb-1">
                Server ID *
              </label>
              <input
                type="text"
                value={newBot.serverId || ""}
                onChange={(e) => setNewBot(prev => ({ ...prev, serverId: e.target.value }))}
                className="w-full rounded-md bg-gray-700 text-white border border-gray-600 p-2"
                placeholder="123456789012345678"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-200 mb-1">
                Channel ID *
              </label>
              <input
                type="text"
                value={newBot.channelId || ""}
                onChange={(e) => setNewBot(prev => ({ ...prev, channelId: e.target.value }))}
                className="w-full rounded-md bg-gray-700 text-white border border-gray-600 p-2"
                placeholder="123456789012345678"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-200 mb-1">
                Client ID
              </label>
              <input
                type="text"
                value={newBot.clientId || ""}
                onChange={(e) => setNewBot(prev => ({ ...prev, clientId: e.target.value }))}
                className="w-full rounded-md bg-gray-700 text-white border border-gray-600 p-2"
                placeholder="Discord Application Client ID"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-200 mb-1">
                Bot Token
              </label>
              <input
                type="password"
                value={newBot.botToken || ""}
                onChange={(e) => setNewBot(prev => ({ ...prev, botToken: e.target.value }))}
                className="w-full rounded-md bg-gray-700 text-white border border-gray-600 p-2"
                placeholder="Discord Bot Token"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-200 mb-1">
                Webhook URL
              </label>
              <input
                type="text"
                value={newBot.webhookUrl || ""}
                onChange={(e) => setNewBot(prev => ({ ...prev, webhookUrl: e.target.value }))}
                className="w-full rounded-md bg-gray-700 text-white border border-gray-600 p-2"
                placeholder={WEBHOOK_GROUP_2}
              />
            </div>
          </div>

          {/* Permissions */}
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-200 mb-2">
              Bot Permissions
            </label>
            <Select
              isMulti
              value={DISCORD_PERMISSIONS.filter(perm => newBot.permissions?.includes(perm.id))}
              onChange={(selectedOptions) => {
                setNewBot(prev => ({
                  ...prev,
                  permissions: selectedOptions ? selectedOptions.map(option => option.id) : []
                }));
              }}
              options={DISCORD_PERMISSIONS}
              getOptionLabel={(option) => option.name}
              getOptionValue={(option) => option.id}
              placeholder="Select permissions..."
              styles={customStyle}
              className="text-sm"
            />
          </div>

          {/* Pattern Configuration */}
          <div className="mt-4">
            <div className="flex justify-between items-center mb-2">
              <label className="block text-sm font-medium text-gray-200">
                Message Parsing Patterns
              </label>
              <button
                type="button"
                onClick={setDefaultPatterns}
                className="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
              >
                Set to Default
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs">
              <div>
                <label className="block text-xs text-gray-300 mb-1">TP Pattern:</label>
                <input
                  type="text"
                  value={newBot.patterns?.tp || DEFAULT_PATTERNS.tp}
                  onChange={(e) => setNewBot(prev => ({
                    ...prev,
                    patterns: { ...prev.patterns, ...DEFAULT_PATTERNS, tp: e.target.value }
                  }))}
                  className="w-full rounded bg-gray-700 text-white border border-gray-600 p-1 text-xs"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-300 mb-1">SL Pattern:</label>
                <input
                  type="text"
                  value={newBot.patterns?.sl || DEFAULT_PATTERNS.sl}
                  onChange={(e) => setNewBot(prev => ({
                    ...prev,
                    patterns: { ...prev.patterns, ...DEFAULT_PATTERNS, sl: e.target.value }
                  }))}
                  className="w-full rounded bg-gray-700 text-white border border-gray-600 p-1 text-xs"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-300 mb-1">Symbol Pattern:</label>
                <input
                  type="text"
                  value={newBot.patterns?.symbol || DEFAULT_PATTERNS.symbol}
                  onChange={(e) => setNewBot(prev => ({
                    ...prev,
                    patterns: { ...prev.patterns, ...DEFAULT_PATTERNS, symbol: e.target.value }
                  }))}
                  className="w-full rounded bg-gray-700 text-white border border-gray-600 p-1 text-xs"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-300 mb-1">Signal Pattern:</label>
                <input
                  type="text"
                  value={newBot.patterns?.signal || DEFAULT_PATTERNS.signal}
                  onChange={(e) => setNewBot(prev => ({
                    ...prev,
                    patterns: { ...prev.patterns, ...DEFAULT_PATTERNS, signal: e.target.value }
                  }))}
                  className="w-full rounded bg-gray-700 text-white border border-gray-600 p-1 text-xs"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-300 mb-1">Price Pattern:</label>
                <input
                  type="text"
                  value={newBot.patterns?.price || DEFAULT_PATTERNS.price}
                  onChange={(e) => setNewBot(prev => ({
                    ...prev,
                    patterns: { ...prev.patterns, ...DEFAULT_PATTERNS, price: e.target.value }
                  }))}
                  className="w-full rounded bg-gray-700 text-white border border-gray-600 p-1 text-xs"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-300 mb-1">Comment Pattern:</label>
                <input
                  type="text"
                  value={newBot.patterns?.comment || DEFAULT_PATTERNS.comment}
                  onChange={(e) => setNewBot(prev => ({
                    ...prev,
                    patterns: { ...prev.patterns, ...DEFAULT_PATTERNS, comment: e.target.value }
                  }))}
                  className="w-full rounded bg-gray-700 text-white border border-gray-600 p-1 text-xs"
                />
              </div>
            </div>
          </div>

          {/* Lot Settings */}
          <div className="mt-4">
            <div className="flex justify-between items-center mb-2">
              <label className="block text-sm font-medium text-gray-200">
                Lot Distribution Settings
              </label>
              <button
                type="button"
                onClick={setDefaultLotSettings}
                className="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
              >
                Set to Default
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              <div>
                <label className="block text-xs text-gray-300 mb-1">Max Lot per Message:</label>
                <input
                  type="number"
                  step="0.01"
                  value={newBot.lotSettings?.maxLotPerMessage || LOT_DISTRIBUTION_CONFIG.maxLotPerMessage}
                  onChange={(e) => setNewBot(prev => ({
                    ...prev,
                    lotSettings: {
                      ...prev.lotSettings,
                      ...LOT_DISTRIBUTION_CONFIG,
                      maxLotPerMessage: parseFloat(e.target.value)
                    }
                  }))}
                  className="w-full rounded bg-gray-700 text-white border border-gray-600 p-1 text-xs"
                />
              </div>
            </div>
          </div>

          <div className="flex gap-2 mt-4">
            <button
              onClick={editingBot ? saveEditBot : addBot}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              {editingBot ? 'Save Changes' : 'Add Bot'}
            </button>
            <button
              onClick={editingBot ? cancelEdit : () => setShowAddBot(false)}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* Bot List */}
      <div className="space-y-4">
        {bots.map((bot) => (
          <div key={bot.id} className="bg-gray-800 p-4 rounded-lg border border-gray-600">
            <div className="flex justify-between items-start mb-3">
              <div>
                <h3 className="text-lg font-semibold text-white">{bot.name}</h3>
                <p className="text-sm text-gray-400">
                  Server: {bot.serverId} | Channel: {bot.channelId}
                </p>
                <p className="text-sm text-gray-400">
                  Client ID: <span className={bot.clientId ? 'text-green-400' : 'text-red-400'}>
                    {bot.clientId ? '✓ Configured' : '✗ Not configured'}
                  </span>
                </p>
                <p className="text-sm text-gray-400">
                  Status: <span className={bot.enabled ? 'text-green-400' : 'text-red-400'}>
                    {bot.enabled ? 'Active' : 'Inactive'}
                  </span>
                </p>
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => startEditBot(bot)}
                  className="px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700"
                >
                  Edit
                </button>
                <button
                  onClick={() => toggleBot(bot.id)}
                  className={`px-3 py-1 rounded text-sm ${
                    bot.enabled
                      ? 'bg-red-600 hover:bg-red-700'
                      : 'bg-green-600 hover:bg-green-700'
                  } text-white`}
                >
                  {bot.enabled ? 'Stop' : 'Start'}
                </button>
                <button
                  onClick={() => deleteBot(bot.id)}
                  className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>

            {/* Bot Token Status */}
            {!bot.enabled && (
              <div className="mb-3">
                <div className="flex items-center gap-2 mb-2">
                  <label className="block text-sm font-medium text-gray-200">
                    Bot Token Status:
                  </label>
                  <span className={`text-sm ${bot.botToken ? 'text-green-400' : 'text-red-400'}`}>
                    {bot.botToken ? '✓ Configured' : '✗ Not configured'}
                  </span>
                </div>
                {!bot.botToken && (
                  <div>
                    <label className="block text-sm font-medium text-gray-200 mb-1">
                      Temporary Bot Token (use Edit to save permanently)
                    </label>
                    <input
                      type="password"
                      value={botTokens[bot.id] || ""}
                      onChange={(e) => setBotTokens(prev => ({ ...prev, [bot.id]: e.target.value }))}
                      className="w-full rounded-md bg-gray-700 text-white border border-gray-600 p-2"
                      placeholder="Enter bot token..."
                    />
                  </div>
                )}
              </div>
            )}

            {/* Invite Link Generation */}
            <div className="flex gap-2 flex-wrap">
              <button
                onClick={() => generateInviteLink(bot)}
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                Generate Invite Link
              </button>
              
              {inviteLinks[bot.id] && (
                <button
                  onClick={() => copyToClipboard(inviteLinks[bot.id])}
                  className="px-3 py-1 bg-purple-600 text-white rounded text-sm hover:bg-purple-700"
                >
                  Copy Invite Link
                </button>
              )}
            </div>

            {/* Show Invite Link */}
            {inviteLinks[bot.id] && (
              <div className="mt-2 p-2 bg-gray-700 rounded text-xs text-gray-300 break-all">
                {inviteLinks[bot.id]}
              </div>
            )}

            {/* Permissions Display */}
            <div className="mt-3">
              <p className="text-sm text-gray-400 mb-1">Permissions:</p>
              <div className="flex flex-wrap gap-1">
                {bot.permissions.map((perm) => (
                  <span key={perm} className="px-2 py-1 bg-gray-700 text-xs text-gray-300 rounded">
                    {DISCORD_PERMISSIONS.find(p => p.id === perm)?.name || perm}
                  </span>
                ))}
              </div>
            </div>
          </div>
        ))}

        {bots.length === 0 && (
          <div className="text-center py-8 text-gray-400">
            No Discord bots configured. Click "Add Bot" to get started.
          </div>
        )}
      </div>

      {/* Confirmation Dialog for Delete */}
      <ConfirmationDialog
        isOpen={showDeleteConfirm}
        title="Confirm Delete Bot"
        message={`Are you sure you want to delete this Discord bot? This action cannot be undone.`}
        onConfirm={confirmDeleteBot}
        onCancel={cancelDeleteBot}
      />

      {/* Alert Dialog */}
      <AlertDialog
        isOpen={alert.isOpen}
        title={alert.title}
        message={alert.message}
        type={alert.type}
        onClose={closeAlert}
        autoClose={alert.autoClose}
      />
    </div>
  );
}
