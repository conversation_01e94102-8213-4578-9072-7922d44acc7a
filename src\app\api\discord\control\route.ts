// app/api/discord/control/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import { DiscordBotConfig } from '@/types/discord';
import { discordService } from '@/services/discordService';

const BOTS_CONFIG_FILE = path.join(process.cwd(), 'discord-bots.json');

async function loadBots(): Promise<DiscordBotConfig[]> {
  try {
    const data = await fs.readFile(BOTS_CONFIG_FILE, 'utf8');
    return JSON.parse(data);
  } catch {
    return [];
  }
}

async function saveBots(bots: DiscordBotConfig[]): Promise<void> {
  await fs.writeFile(BOTS_CONFIG_FILE, JSON.stringify(bots, null, 2));
}

export async function POST(request: NextRequest) {
  try {
    const { action, botId, token } = await request.json();
    
    const bots = await loadBots();
    const bot = bots.find(b => b.id === botId);
    
    if (!bot) {
      return NextResponse.json({ error: 'Bot not found' }, { status: 404 });
    }

    if (action === 'start') {
      if (!token) {
        return NextResponse.json({ error: 'Bot token is required' }, { status: 400 });
      }

      const result = await discordService.startBot(bot, token);
      
      if (result.success) {
        // Update bot status in config
        const updatedBots = bots.map(b => 
          b.id === botId ? { ...b, enabled: true, lastActive: new Date().toISOString() } : b
        );
        await saveBots(updatedBots);
        
        return NextResponse.json({ success: true, message: 'Bot started successfully' });
      } else {
        return NextResponse.json({ error: result.error || 'Failed to start bot' }, { status: 500 });
      }
    } else if (action === 'stop') {
      await discordService.stopBot(botId);
      
      // Update bot status in config
      const updatedBots = bots.map(b => 
        b.id === botId ? { ...b, enabled: false } : b
      );
      await saveBots(updatedBots);
      
      return NextResponse.json({ success: true, message: 'Bot stopped successfully' });
    } else {
      return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Discord bot control error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
