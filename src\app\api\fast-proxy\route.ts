import { NextRequest, NextResponse } from 'next/server';

const WEBHOOK_URL = process.env.NEXT_PUBLIC_WEBHOOK_URL_GROUP1;

export async function POST(request: NextRequest) {
  if (!WEBHOOK_URL) {
    return NextResponse.json({ error: 'Webhook not configured' }, { status: 500 });
  }

  try {
    // Get JSON body directly
    const body = await request.json();
    const accessToken = process.env.WEBHOOK_ACCESS_TOKEN;
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'NextJS-Webhook-Proxy/1.0',
    };
    // Add access token to headers if available
    if (accessToken) {
      headers['Authorization'] = `Bearer ${accessToken}`;
      headers['X-Access-Token'] = accessToken;
    }

    // Fast forward - minimal headers, no logging
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(body),
    });

    // Return response directly
    const data = await response.json();
    return NextResponse.json(data, { status: response.status });

  } catch {
    return NextResponse.json({ error: 'Request failed' }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({ 
    message: 'Fast JSON proxy - POST only',
    target: WEBHOOK_URL 
  });
}
