// types/discord.ts
export interface DiscordBotConfig {
  id: string;
  name: string;
  enabled: boolean;
  serverId: string;
  channelId: string;
  clientId?: string;
  botToken?: string;
  webhookUrl: string;
  permissions: string[];
  createdAt: string;
  lastActive?: string;
  patterns?: {
    tp: string;
    sl: string;
    symbol: string;
    signal: string;
    price: string;
    comment: string;
  };
  lotSettings?: {
    maxLotPerMessage: number;
    lotDistribution: {
      [key: number]: readonly number[]; // key is number of TPs, value is percentage array
    };
    minLotSize?: number;
    description?: string;
  };
}

export interface DiscordMessage {
  id: string;
  content: string;
  author: {
    id: string;
    username: string;
    discriminator: string;
  };
  timestamp: string;
  channelId: string;
  serverId: string;
}

export const DISCORD_PERMISSIONS = [
  { id: 'VIEW_CHANNEL', name: 'View Channel', description: 'Allows the bot to view channels', default: true },
  { id: 'READ_MESSAGE_HISTORY', name: 'Read Message History', description: 'Allows the bot to read message history', default: true },
  { id: 'SEND_MESSAGES', name: 'Send Messages', description: 'Allows the bot to send messages', default: false },
  { id: 'MANAGE_MESSAGES', name: 'Manage Messages', description: 'Allows the bot to delete and edit messages', default: false },
  { id: 'EMBED_LINKS', name: 'Embed Links', description: 'Allows the bot to embed links', default: false },
  { id: 'ATTACH_FILES', name: 'Attach Files', description: 'Allows the bot to attach files', default: false },
  { id: 'USE_EXTERNAL_EMOJIS', name: 'Use External Emojis', description: 'Allows the bot to use external emojis', default: false },
  { id: 'ADD_REACTIONS', name: 'Add Reactions', description: 'Allows the bot to add reactions', default: false },
  { id: 'MENTION_EVERYONE', name: 'Mention Everyone', description: 'Allows the bot to mention @everyone and @here', default: false },
  { id: 'MANAGE_WEBHOOKS', name: 'Manage Webhooks', description: 'Allows the bot to manage webhooks', default: false },
  { id: 'USE_SLASH_COMMANDS', name: 'Use Slash Commands', description: 'Allows the bot to use slash commands', default: false }
] as const;

export const DEFAULT_PATTERNS = {
  tp: 'TP\\d\\s*:\\s*(\\d+(\\.\\d+)?)',
  sl: 'SL\\s*:\\s*(\\d+(\\.\\d+)?)',
  symbol: 'Symbol\\s*:\\s*(\\w+)',
  signal: 'Signal\\s*:\\s*(.+)',
  price: 'Price\\s*:\\s*(\\d+(\\.\\d+)?)\\s*[-–]\\s*(\\d+(\\.\\d+)?)',
  comment: 'C\\.(\\w+)'
};

// Import from global config instead of defining here
import { LOT_DISTRIBUTION_CONFIG } from '@/config/global';

export const DEFAULT_LOT_SETTINGS = LOT_DISTRIBUTION_CONFIG;
