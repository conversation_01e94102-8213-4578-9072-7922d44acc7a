// components/FloatingTextareaButtons.tsx
"use client";
import { useState } from 'react';

interface FloatingTextareaButtonsProps {
  onCopy: () => void;
  onPaste: () => void;
  onClear?: () => void;
  className?: string;
}

const FloatingTextareaButtons: React.FC<FloatingTextareaButtonsProps> = ({
  onCopy,
  onPaste,
  onClear,
  className = ""
}) => {
  const [showTooltip, setShowTooltip] = useState<string | null>(null);

  return (
    <div className={`absolute bottom-2 right-2 flex gap-1 ${className}`}>
      {onClear && (
        <div className="relative">
          <button
            type="button"
            onClick={onClear}
            onMouseEnter={() => setShowTooltip('clear')}
            onMouseLeave={() => setShowTooltip(null)}
            className="p-1.5 bg-red-700 hover:bg-red-600 text-white rounded-md shadow-lg transition-colors duration-200 text-xs"
            title="Clear all data"
          >
            🗑️
          </button>
          {showTooltip === 'clear' && (
            <div className="absolute bottom-full right-0 mb-1 px-2 py-1 bg-gray-800 text-white text-xs rounded whitespace-nowrap">
              Clear
            </div>
          )}
        </div>
      )}

      <div className="relative">
        <button
          type="button"
          onClick={onPaste}
          onMouseEnter={() => setShowTooltip('paste')}
          onMouseLeave={() => setShowTooltip(null)}
          className="p-1.5 bg-gray-700 hover:bg-gray-600 text-white rounded-md shadow-lg transition-colors duration-200 text-xs"
          title="Paste from clipboard"
        >
          📋
        </button>
        {showTooltip === 'paste' && (
          <div className="absolute bottom-full right-0 mb-1 px-2 py-1 bg-gray-800 text-white text-xs rounded whitespace-nowrap">
            Paste
          </div>
        )}
      </div>
      
      {/* <div className="relative">
        <button
          type="button"
          onClick={onCopy}
          onMouseEnter={() => setShowTooltip('copy')}
          onMouseLeave={() => setShowTooltip(null)}
          className="p-1.5 bg-gray-700 hover:bg-gray-600 text-white rounded-md shadow-lg transition-colors duration-200 text-xs"
          title="Copy to clipboard"
        >
          📄
        </button>
        {showTooltip === 'copy' && (
          <div className="absolute bottom-full right-0 mb-1 px-2 py-1 bg-gray-800 text-white text-xs rounded whitespace-nowrap">
            Copy
          </div>
        )}
      </div> */}
    </div>
  );
};

export default FloatingTextareaButtons;
