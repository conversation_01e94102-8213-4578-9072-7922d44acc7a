// hooks/useAlert.ts
import { useState, useCallback } from 'react';

interface AlertState {
  isOpen: boolean;
  title?: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  autoClose?: number;
}

export const useAlert = () => {
  const [alert, setAlert] = useState<AlertState>({
    isOpen: false,
    message: '',
    type: 'info'
  });

  const showAlert = useCallback((
    message: string, 
    type: 'success' | 'error' | 'warning' | 'info' = 'info',
    title?: string,
    autoClose?: number
  ) => {
    setAlert({
      isOpen: true,
      message,
      type,
      title,
      autoClose
    });
  }, []);

  const showSuccess = useCallback((message: string, title?: string, autoClose = 3000) => {
    showAlert(message, 'success', title, autoClose);
  }, [showAlert]);

  const showError = useCallback((message: string, title?: string) => {
    showAlert(message, 'error', title);
  }, [showAlert]);

  const showWarning = useCallback((message: string, title?: string) => {
    showAlert(message, 'warning', title);
  }, [showAlert]);

  const showInfo = useCallback((message: string, title?: string) => {
    showAlert(message, 'info', title);
  }, [showAlert]);

  const closeAlert = useCallback(() => {
    setAlert(prev => ({ ...prev, isOpen: false }));
  }, []);

  return {
    alert,
    showAlert,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    closeAlert
  };
};
