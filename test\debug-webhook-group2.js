// Debug Group 2 webhook parameters
const http = require('http');

const WEBHOOK_GROUP_2 = 'http://nas-pm.suksapan.or.th:2249/webhook_input';

// Test different parameter combinations for Group 2
const testCases = [
  {
    name: "Original Group 2 format",
    data: {
      rows: [{ tp: "2000", lot: "0.01" }],
      a: "Buy Limit",
      p: "1950.00",
      sl: "1900",
      s: "XAUUSD",
      c: "SIG_TEST"
    }
  },
  {
    name: "Minimal Group 2 format",
    data: {
      a: "Buy Limit",
      s: "XAUUSD",
      p: "1950.00"
    }
  },
  {
    name: "With rows only",
    data: {
      rows: [{ tp: "2000", lot: "0.01" }],
      a: "Buy Limit",
      s: "XAUUSD"
    }
  }
];

function testWebhook(url, testCase) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(testCase.data);
    
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log(`\n${'='.repeat(60)}`);
    console.log(`Testing: ${testCase.name}`);
    console.log(`Data: ${postData}`);
    console.log(`${'='.repeat(60)}`);

    const req = http.request(options, (res) => {
      console.log(`Status: ${res.statusCode} ${res.statusMessage}`);
      
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        console.log(`Response: ${responseData}`);
        
        if (responseData.includes('Success') || responseData.includes('"error":false')) {
          console.log('✅ SUCCESS!');
        } else if (responseData.includes('Missing')) {
          console.log('❌ Missing required fields');
        } else if (responseData.includes('Internal Server Error')) {
          console.log('💥 Server error');
        } else {
          console.log('⚠️ Unknown response');
        }
        
        resolve({ status: res.statusCode, data: responseData });
      });
    });

    req.on('error', (err) => {
      console.log(`❌ Request Error: ${err.message}`);
      reject(err);
    });

    req.setTimeout(10000);
    req.write(postData);
    req.end();
  });
}

async function runTests() {
  console.log('🔍 Debugging Group 2 webhook parameters...\n');
  
  for (const testCase of testCases) {
    try {
      await testWebhook(WEBHOOK_GROUP_2, testCase);
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between tests
    } catch (error) {
      console.log(`Failed: ${error.message}`);
    }
  }
  
  console.log('\n✅ All tests completed!');
}

runTests();
