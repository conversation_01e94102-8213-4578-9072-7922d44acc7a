"use client";
import React, { createContext, useContext, useState, useEffect } from 'react';

interface AuthContextType {
  isAuthenticated: boolean;
  login: (pin: string) => boolean;
  logout: () => void;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  // Security configuration
  const CORRECT_PIN = "1212"; // Change this to your preferred PIN
  const AUTH_KEY = "app_auth_session";

  useEffect(() => {
    // Check if user is already authenticated
    const checkAuth = () => {
      try {
        const authStatus = sessionStorage.getItem(AUTH_KEY);
        const authTime = sessionStorage.getItem(AUTH_KEY + "_time");
        
        if (authStatus === "authenticated" && authTime) {
          const loginTime = parseInt(authTime);
          const currentTime = Date.now();
          const sessionDuration = 24 * 60 * 60 * 1000; // 24 hours
          
          if (currentTime - loginTime < sessionDuration) {
            setIsAuthenticated(true);
          } else {
            // Session expired
            sessionStorage.removeItem(AUTH_KEY);
            sessionStorage.removeItem(AUTH_KEY + "_time");
          }
        }
      } catch (error) {
        console.error("Auth check error:", error);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = (pin: string): boolean => {
    if (pin === CORRECT_PIN) {
      setIsAuthenticated(true);
      sessionStorage.setItem(AUTH_KEY, "authenticated");
      sessionStorage.setItem(AUTH_KEY + "_time", Date.now().toString());
      return true;
    }
    return false;
  };

  const logout = () => {
    setIsAuthenticated(false);
    sessionStorage.removeItem(AUTH_KEY);
    sessionStorage.removeItem(AUTH_KEY + "_time");
  };

  const value = {
    isAuthenticated,
    login,
    logout,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
