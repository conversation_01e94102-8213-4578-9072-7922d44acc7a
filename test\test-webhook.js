// Simple Node.js script to test webhook connectivity
// Run with: node test-webhook.js

const https = require('https');
const http = require('http');

const WEBHOOK_GROUP_1 = 'http://nas-pm.suksapan.or.th:2249/webhook_instant';
const WEBHOOK_GROUP_2 = 'http://nas-pm.suksapan.or.th:2249/webhook_input';

const testData = {
  a: "Buy Now",
  ptp: "2000",
  psl: "1000",
  lot: "0.01",
  s: "XAUUSD",
  c: "SIG_TEST"
};

function testWebhook(url, data) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const postData = JSON.stringify(data);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'User-Agent': 'Node.js Test Script'
      }
    };

    console.log(`\n🔍 Testing: ${url}`);
    console.log(`📤 Sending data:`, data);

    const req = client.request(options, (res) => {
      console.log(`📊 Status: ${res.statusCode} ${res.statusMessage}`);
      console.log(`📋 Headers:`, res.headers);
      
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        console.log(`📥 Response:`, responseData);
        resolve({ status: res.statusCode, data: responseData, headers: res.headers });
      });
    });

    req.on('error', (err) => {
      console.log(`❌ Error:`, err.message);
      reject(err);
    });

    req.on('timeout', () => {
      console.log(`⏰ Request timeout`);
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.setTimeout(10000); // 10 second timeout
    req.write(postData);
    req.end();
  });
}

async function runTests() {
  console.log('🚀 Starting webhook connectivity tests...\n');
  
  try {
    console.log('='.repeat(50));
    console.log('Testing Group 1 Webhook');
    console.log('='.repeat(50));
    await testWebhook(WEBHOOK_GROUP_1, testData);
    
    console.log('\n' + '='.repeat(50));
    console.log('Testing Group 2 Webhook');
    console.log('='.repeat(50));
    await testWebhook(WEBHOOK_GROUP_2, {
      rows: [{ tp: "2000", lot: "0.01" }],
      a: "Buy Limit",
      p: "1950.00",
      sl: "1900",
      s: "XAUUSD",
      c: "SIG_TEST"
    });
    
  } catch (error) {
    console.log(`\n❌ Test failed:`, error.message);
  }
  
  console.log('\n✅ Tests completed!');
}

runTests();
