// components/TabInstant.tsx
"use client";
import { useState } from "react";
import Select from "react-select";
import { SYMBOL_OPTIONS } from "@/config/global";
import { useConfirmation } from "@/contexts/ConfirmationContext";
import ConfirmationDialog from "./ConfirmationDialog";

const WEBHOOK_GROUP_1 = process.env.NEXT_PUBLIC_WEBHOOK_URL_GROUP1;
 
export default function TabInstant({
  customStyle,
  sendWebhook,
  loading,
}: {
  customStyle: Object;
  sendWebhook: Function;
  loading: boolean;
}) {  
  const [data, setData] = useState({
    // p: "1950.00",    // price - required for Group 1
    ptp: 2000,
    psl: 1000,
    lot: 0.01,
    s: "XAUUSD",
    c: "ZD_INSTANT",
  });

  // Get confirmation setting from context
  const { requireConfirmation } = useConfirmation();

  // Confirmation dialog state
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [pendingAction, setPendingAction] = useState<string>("");

  const handleSubmitClick = (a: string) => {
    if (requireConfirmation) {
      setPendingAction(a);
      setShowConfirmation(true);
    } else {
      // Send immediately without confirmation
      handleDirectSubmit(a);
    }
  };

  const handleDirectSubmit = (a: string) => {
    // Group 1 webhook only accepts minimal fields: a, s, p
    // Adding extra fields causes 500 Internal Server Error
    const webhookData = {
      a,         // action: "Buy Now" or "Sell Now"
      ...data
      // s: data.s, // symbol: "XAUUSD"
      // p: data.p  // price: required field
    };

    sendWebhook("g_instant", WEBHOOK_GROUP_1, webhookData);
  };

  const handleConfirmSubmit = () => {
    handleDirectSubmit(pendingAction);
    setShowConfirmation(false);
    setPendingAction("");
  };

  const handleCancelSubmit = () => {
    setShowConfirmation(false);
    setPendingAction("");
  };
  
  return (
    <div className="space-y-4"> 

      <div className="flex flex-col sm:flex-row grid grid-cols-2 lg:grid-cols-2 gap-4 items-center">

        {/* Symbol */}
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-200">S</label>
          <Select
            className="text-black"
            // className="mt-1 w-full rounded-md bg-gray-800 text-white border border-gray-600 p-2"
            // value={data.symbol}
            value={SYMBOL_OPTIONS.find((opt) => opt.value === data.s)}
            // value={{label: data.symbol, value: data.symbol }}
            onChange={(e) => setData({ ...data, s: e?.value || "" })}
            options={SYMBOL_OPTIONS}
            isSearchable
            styles={customStyle}
          />
        </div>

        {/* Signal Type */}
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-200">
            L
          </label>
          <input
            type="text"
            className="w-full rounded-md bg-gray-800 text-white border border-gray-600 p-2"
            value={data.lot}
            onChange={(e) => setData({ ...data, lot: Number(e.target.value) })}
          />
        </div>
      </div>
      <div className="flex flex-col sm:flex-row grid grid-cols-2 lg:grid-cols-2 gap-4 items-center">

        {/* TP - Optional (not sent to webhook) */}
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-200">TP</label>
          <input
            type="text"
            className="w-full rounded-md bg-gray-800 text-white border border-gray-600 p-2 opacity-60"
            value={data.ptp}
            onChange={(e) => setData({ ...data, ptp: Number(e.target.value) })}
            placeholder="Not sent to webhook"
          />
        </div>
        
        {/* SL - Optional (not sent to webhook) */}
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-200">SL</label>
          <input
            type="text"
            className="w-full rounded-md bg-gray-800 text-white border border-gray-600 p-2 opacity-60"
            value={data.psl}
            onChange={(e) => setData({ ...data, psl: Number(e.target.value) })}
            placeholder="Not sent to webhook"
          />
        </div>
      </div>
 
      {/* Comment */}
      <div>
        <label className="block text-sm font-medium text-gray-200">Comment</label>
        <input
          type="text"
          className="mt-1 w-full rounded-md bg-gray-800 text-white border border-gray-600 p-2"
          value={data.c}
          onChange={(e) => setData({ ...data, c: e.target.value })}
        />
      </div>

      {/* Submit */}
      <div className="flex flex-col sm:flex-row grid grid-cols-2 lg:grid-cols-2 gap-4 items-center">
        <button
          disabled={loading}
          onClick={() => handleSubmitClick("Sell Now")}
          className="w-full py-2 px-4 bg-red-600 text-white font-semibold rounded-md hover:bg-red-700 disabled:opacity-50"
        >
          {loading ? "⏳ กำลังส่ง..." : "SELL"}
        </button>
        <button
          disabled={loading}
          onClick={() => handleSubmitClick("Buy Now")}
          className="w-full py-2 px-4 bg-green-600 text-white font-semibold rounded-md hover:bg-green-700 disabled:opacity-50"
        >
          {loading ? "⏳ กำลังส่ง..." : "BUY"}
        </button>
      </div>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showConfirmation}
        title="Confirm Webhook Send"
        message={`Are you sure you want to send ${pendingAction} signal for ${data.s}?`}
        confirmText="Send Webhook"
        cancelText="Cancel"
        onConfirm={handleConfirmSubmit}
        onCancel={handleCancelSubmit}
        type="warning"
        data={{
          action: pendingAction,
          symbol: data.s,
          lot: data.lot,
          comment: data.c
        }}
      />
    </div>
  );
}
