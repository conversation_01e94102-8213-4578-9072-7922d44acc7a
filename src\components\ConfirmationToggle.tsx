"use client";
import React from 'react';
import { useConfirmation } from '@/contexts/ConfirmationContext';

const ConfirmationToggle: React.FC = () => {
  const { requireConfirmation, toggleConfirmation } = useConfirmation();

  return (
    <div className="flex items-center space-x-3">
      {/* <span className="text-sm text-gray-300">
        {requireConfirmation ? '✅' : '⚡'} Confirmation
      </span> */}
      
      <button
        onClick={toggleConfirmation}
        className={`ml-2 relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800 ${
          requireConfirmation 
            ? 'bg-blue-600' 
            : 'bg-gray-600'
        }`}
        role="switch"
        aria-checked={requireConfirmation}
        aria-label="Toggle webhook confirmation"
      >
        <span
          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
            requireConfirmation ? 'translate-x-6' : 'translate-x-1'
          }`}
        />
      </button>
      
      {/* <span className="text-xs text-gray-400">
        {requireConfirmation ? 'ON' : 'OFF'}
      </span> */}
      
      <div className="text-xs text-gray-300 ">
        {requireConfirmation 
          ? 'Require confirmation ✅ ' 
          : 'Send instantly ⚡ '
        }
      </div>
    </div>
  );
};

export default ConfirmationToggle;
