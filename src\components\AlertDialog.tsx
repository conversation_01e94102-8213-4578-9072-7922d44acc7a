// components/AlertDialog.tsx
"use client";
import { useEffect } from 'react';

interface AlertDialogProps {
  isOpen: boolean;
  title?: string;
  message: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  onClose: () => void;
  autoClose?: number; // Auto close after X milliseconds
}

const AlertDialog: React.FC<AlertDialogProps> = ({
  isOpen,
  title,
  message,
  type = 'info',
  onClose,
  autoClose
}) => {
  useEffect(() => {
    if (isOpen && autoClose) {
      const timer = setTimeout(() => {
        onClose();
      }, autoClose);
      return () => clearTimeout(timer);
    }
  }, [isOpen, autoClose, onClose]);

  if (!isOpen) return null;

  const getTypeStyles = () => {
    switch (type) {
      case 'success':
        return {
          bg: 'bg-green-900 border-green-600',
          text: 'text-green-200',
          icon: '✅',
          button: 'bg-green-600 hover:bg-green-700'
        };
      case 'error':
        return {
          bg: 'bg-red-900 border-red-600',
          text: 'text-red-200',
          icon: '❌',
          button: 'bg-red-600 hover:bg-red-700'
        };
      case 'warning':
        return {
          bg: 'bg-yellow-900 border-yellow-600',
          text: 'text-yellow-200',
          icon: '⚠️',
          button: 'bg-yellow-600 hover:bg-yellow-700'
        };
      default:
        return {
          bg: 'bg-blue-900 border-blue-600',
          text: 'text-blue-200',
          icon: 'ℹ️',
          button: 'bg-blue-600 hover:bg-blue-700'
        };
    }
  };

  const styles = getTypeStyles();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`${styles.bg} border-2 rounded-lg p-6 max-w-md w-full mx-4 shadow-2xl`}>
        <div className="flex items-center mb-4">
          <span className="text-2xl mr-3">{styles.icon}</span>
          <h3 className={`text-lg font-semibold ${styles.text}`}>
            {title || (type === 'success' ? 'Success' : 
                      type === 'error' ? 'Error' : 
                      type === 'warning' ? 'Warning' : 'Information')}
          </h3>
        </div>
        
        <p className={`${styles.text} mb-6 leading-relaxed`}>
          {message}
        </p>
        
        <div className="flex justify-end">
          <button
            onClick={onClose}
            className={`px-6 py-2 ${styles.button} text-white rounded-md font-medium transition-colors duration-200`}
          >
            OK
          </button>
        </div>
      </div>
    </div>
  );
};

export default AlertDialog;
