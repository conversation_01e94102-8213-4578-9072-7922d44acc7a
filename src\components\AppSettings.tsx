// components/AppSettings.tsx
"use client";
import { useState } from 'react';
import { useAppSettings } from '@/contexts/AppSettingsContext';

export default function AppSettings() {
  const { settings, toggleTabVisibility, toggleTabNavigation } = useAppSettings();
  const [showSettings, setShowSettings] = useState(false);

  const tabLabels = {
    instant: 'Instant Tab',
    input: 'Input Tab',
    control: 'Control Tab',
    calendar: 'Calendar Tab',
    discord: 'Discord Bot Tab',
    orders: 'Orders Tab',
  };

  return (
    <div className="relative">
      <button
        onClick={() => setShowSettings(!showSettings)}
        className="flex items-center space-x-2 px-3 py-2 text-gray-300 hover:text-white transition-colors"
        title="App Settings"
      >
        <span className="text-lg">⚙️</span>
        <span className="hidden sm:inline">Settings</span>
      </button>

      {showSettings && (
        <div className="absolute right-0 top-full mt-2 w-80 bg-gray-800 border border-gray-600 rounded-lg shadow-lg z-50">
          <div className="p-4">
            <h3 className="text-lg font-semibold text-white mb-4">App Settings</h3>
            
            {/* Tab Navigation Toggle */}
            <div className="mb-4">
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={settings.showTabNavigation}
                  onChange={toggleTabNavigation}
                  className="rounded"
                />
                <span className="text-gray-200">Show Tab Navigation</span>
              </label>
              <p className="text-xs text-gray-400 mt-1">
                Hide tab navigation when presenting to others
              </p>
            </div>

            {/* Individual Tab Visibility */}
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-200 mb-2">Visible Tabs</h4>
              <div className="space-y-2">
                {Object.entries(settings.visibleTabs).map(([tab, visible]) => (
                  <label key={tab} className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={visible}
                      onChange={() => toggleTabVisibility(tab as keyof typeof settings.visibleTabs)}
                      className="rounded"
                      disabled={!settings.showTabNavigation}
                    />
                    <span className={`text-sm ${settings.showTabNavigation ? 'text-gray-200' : 'text-gray-500'}`}>
                      {tabLabels[tab as keyof typeof tabLabels]}
                    </span>
                  </label>
                ))}
              </div>
              {!settings.showTabNavigation && (
                <p className="text-xs text-gray-500 mt-2">
                  Enable tab navigation to configure individual tabs
                </p>
              )}
            </div>

            {/* Security Info */}
            <div className="border-t border-gray-600 pt-4">
              <h4 className="text-sm font-medium text-gray-200 mb-2">Security Features</h4>
              <div className="text-xs text-gray-400 space-y-1">
                <p>• Access token authentication for webhooks</p>
                <p>• Secure Discord bot token handling</p>
                <p>• Tab visibility controls for presentations</p>
              </div>
            </div>

            {/* Close Button */}
            <div className="flex justify-end mt-4">
              <button
                onClick={() => setShowSettings(false)}
                className="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
