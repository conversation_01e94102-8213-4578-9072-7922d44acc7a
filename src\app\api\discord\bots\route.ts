// app/api/discord/bots/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import { DiscordBotConfig } from '@/types/discord';
// Import discordService only when needed to avoid build issues
// import { discordService } from '@/services/discordService';

const BOTS_CONFIG_FILE = path.join(process.cwd(), 'discord-bots.json');

async function loadBots(): Promise<DiscordBotConfig[]> {
  try {
    const data = await fs.readFile(BOTS_CONFIG_FILE, 'utf8');
    return JSON.parse(data);
  } catch {
    // File doesn't exist or is invalid, return empty array
    return [];
  }
}

async function saveBots(bots: DiscordBotConfig[]): Promise<void> {
  await fs.writeFile(BOTS_CONFIG_FILE, JSON.stringify(bots, null, 2));
}

export async function GET() {
  try {
    const bots = await loadBots();
    // For now, just return bots without runtime status to avoid build issues
    // TODO: Add runtime status check when discord service is properly initialized
    return NextResponse.json(bots);
  } catch (error) {
    console.error('Failed to load Discord bots:', error);
    return NextResponse.json({ error: 'Failed to load bots' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const bots: DiscordBotConfig[] = await request.json();
    await saveBots(bots);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to save Discord bots:', error);
    return NextResponse.json({ error: 'Failed to save bots' }, { status: 500 });
  }
}
