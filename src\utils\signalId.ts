// utils/signalId.ts

/**
 * Extracts Signal ID from text content
 * Looks for patterns like "Signal ID : 0fddd2a0" or "Signal ID: 0fddd2a0"
 */
export function extractSignalId(text: string): string | null {
  const lines = text.split('\n');
  
  for (const line of lines) {
    const trimmedLine = line.trim();
    
    // Look for "Signal ID : <id>" or "Signal ID: <id>"
    const signalIdMatch = trimmedLine.match(/Signal\s+ID\s*:\s*([a-z0-9]+)/i);
    if (signalIdMatch) {
      return signalIdMatch[1].toLowerCase();
    }
  }
  
  return null;
}

/**
 * Generates a random Signal ID (8 characters, lowercase a-z + 0-9)
 */
export function generateSignalId(): string {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}

/**
 * Gets Signal ID from text, extracting if present or generating if not
 */
export function getOrGenerateSignalId(text: string): string {
  const extracted = extractSignalId(text);
  return extracted || generateSignalId();
}
