"use client";
import React, { createContext, useContext, useState, useEffect } from 'react';

interface ConfirmationContextType {
  requireConfirmation: boolean;
  toggleConfirmation: () => void;
}

const ConfirmationContext = createContext<ConfirmationContextType | undefined>(undefined);

export const useConfirmation = () => {
  const context = useContext(ConfirmationContext);
  if (context === undefined) {
    throw new Error('useConfirmation must be used within a ConfirmationProvider');
  }
  return context;
};

interface ConfirmationProviderProps {
  children: React.ReactNode;
}

export const ConfirmationProvider: React.FC<ConfirmationProviderProps> = ({ children }) => {
  const [requireConfirmation, setRequireConfirmation] = useState(true); // Default to requiring confirmation

  const CONFIRMATION_KEY = "webhook_confirmation_setting";

  useEffect(() => {
    // Load setting from localStorage
    const saved = localStorage.getItem(CONFIRMATION_KEY);
    if (saved !== null) {
      setRequireConfirmation(JSON.parse(saved));
    }
  }, []);

  const toggleConfirmation = () => {
    const newValue = !requireConfirmation;
    setRequireConfirmation(newValue);
    localStorage.setItem(CONFIRMATION_KEY, JSON.stringify(newValue));
  };

  const value = {
    requireConfirmation,
    toggleConfirmation
  };

  return (
    <ConfirmationContext.Provider value={value}>
      {children}
    </ConfirmationContext.Provider>
  );
};
