// contexts/AppSettingsContext.tsx
"use client";
import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface AppSettings {
  showTabNavigation: boolean;
  visibleTabs: {
    instant: boolean;
    input: boolean;
    control: boolean;
    calendar: boolean;
    discord: boolean;
    orders: boolean;
  };
}

interface AppSettingsContextType {
  settings: AppSettings;
  updateSettings: (newSettings: Partial<AppSettings>) => void;
  toggleTabVisibility: (tab: keyof AppSettings['visibleTabs']) => void;
  toggleTabNavigation: () => void;
}

const defaultSettings: AppSettings = {
  showTabNavigation: true,
  visibleTabs: {
    instant: true,
    input: true,
    control: true,
    calendar: false,
    discord: false,
    orders: true,
  },
};

const AppSettingsContext = createContext<AppSettingsContextType | undefined>(undefined);

export function AppSettingsProvider({ children }: { children: ReactNode }) {
  const [settings, setSettings] = useState<AppSettings>(defaultSettings);

  // Load settings from localStorage on mount
  useEffect(() => {
    try {
      const savedSettings = localStorage.getItem('app-settings');
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings);
        setSettings({ ...defaultSettings, ...parsed });
      }
    } catch (error) {
      console.error('Failed to load app settings:', error);
    }
  }, []);

  // Save settings to localStorage whenever they change
  useEffect(() => {
    try {
      localStorage.setItem('app-settings', JSON.stringify(settings));
    } catch (error) {
      console.error('Failed to save app settings:', error);
    }
  }, [settings]);

  const updateSettings = (newSettings: Partial<AppSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  const toggleTabVisibility = (tab: keyof AppSettings['visibleTabs']) => {
    setSettings(prev => ({
      ...prev,
      visibleTabs: {
        ...prev.visibleTabs,
        [tab]: !prev.visibleTabs[tab],
      },
    }));
  };

  const toggleTabNavigation = () => {
    setSettings(prev => ({
      ...prev,
      showTabNavigation: !prev.showTabNavigation,
    }));
  };

  return (
    <AppSettingsContext.Provider value={{
      settings,
      updateSettings,
      toggleTabVisibility,
      toggleTabNavigation,
    }}>
      {children}
    </AppSettingsContext.Provider>
  );
}

export function useAppSettings() {
  const context = useContext(AppSettingsContext);
  if (context === undefined) {
    throw new Error('useAppSettings must be used within an AppSettingsProvider');
  }
  return context;
}
