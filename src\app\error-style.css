/* Copyright 1999-2023. Plesk International GmbH. All rights reserved. */
html {
    overflow-y: scroll;
    color: #000;
    font: 400 62.5%/1.4 "Helvetica Neue", Helvetica, Arial, sans-serif;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
}
body,
html {
    height: 100%;
    min-height: 100%;
}
body {
    margin: 0;
    font-size: 1.3rem;
    background: #fff;
    color: #000;
}
a {
    cursor: pointer;
    text-decoration: none;
    color: #2498e3;
    background-color: transparent;
}
a:active,
a:hover {
    text-decoration: underline;
    color: #188dd9;
    outline: 0;
}
h1,
h2 {
    margin: 0 0 0.5rem;
    color: #444;
    font-weight: 400;
    line-height: 1;
}
h1 { font-size: 2.4rem; }
h2 { font-size: 3.6rem; }
.error-code {
    color: #f47755;
    font-size: 8rem;
    line-height: 1;
}
p { margin: 1.2rem 0; }
p.lead {
    font-size: 1.6rem;
    color: #4f5a64;
}
hr {
    box-sizing: content-box;
    height: 0;
    margin: 2.4rem 0;
    border: 0;
    border-top: 1px solid #ddd;
}
.page {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    min-height: 100vh;
}
.page:before {
    display: block;
    content: '';
    -webkit-box-flex: 0;
    -ms-flex: 0 1 474px;
    flex: 0 1 474px;
    background: #38444f url(error-server.svg) 50% 6em no-repeat;
    background-size: 44% auto;
}
.main {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 70%;
    flex: 1 1 70%;
    box-sizing: border-box;
    padding: 10rem 5rem 5rem;
    min-height: 100vh;
}
.error-description {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
}
.help-actions a {
    display: inline-block;
    border: 2px solid #23a7de;
    margin: 0 0.5rem 0.5rem 0;
    padding: 0.5rem 1rem;
    text-decoration: none;
    -webkit-transition: 0.25s ease;
    transition: 0.25s ease;
}
.help-actions a:hover {
    text-decoration: none;
    background: #23a7de;
    color: #fff;
}
@media (max-width:959px) {
    .page:before {
        -ms-flex-preferred-size: 400px;
        flex-basis: 400px;
        background-position: 50% 4rem;
    }
    .main { padding: 5rem; }
}
@media (max-width: 769px) {
    .page {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
    }
    .page:before {
        -ms-flex-preferred-size: 250px;
        flex-basis: 250px;
        background-position: 5rem -4.8rem;
        background-size: 166px auto;
    }
    .main {  /* for IE 11 */
        min-height: 0;
        -webkit-box-flex: 0;
        -ms-flex: none;
        flex: none;
    }
}
@media (max-width: 479px) {
    h2 { font-size: 3rem; }
    .main { padding: 3rem; }
}